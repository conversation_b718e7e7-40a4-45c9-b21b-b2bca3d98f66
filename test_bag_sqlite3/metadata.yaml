rosbag2_bagfile_information:
  compression_format: ''
  compression_mode: ''
  duration:
    nanoseconds: 300000000
  files:
  - duration:
      nanoseconds: 300000000
    message_count: 4
    path: test_bag_sqlite3.db3
    starting_time:
      nanoseconds_since_epoch: 1749314381032648448
  message_count: 4
  relative_file_paths:
  - test_bag_sqlite3.db3
  starting_time:
    nanoseconds_since_epoch: 1749314381032648448
  storage_identifier: sqlite3
  topics_with_message_count:
  - message_count: 2
    topic_metadata:
      name: /test/std_msgs/int16
      offered_qos_profiles: ''
      serialization_format: cdr
      type: std_msgs/msg/Int16
  - message_count: 2
    topic_metadata:
      name: /test/geometry_msgs/point32
      offered_qos_profiles: ''
      serialization_format: cdr
      type: geometry_msgs/msg/Point32
  version: 5
