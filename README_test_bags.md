# ROS2 Test Bag Generator

This script generates test ROS2 bag files in both SQLite3 and MCAP formats that are compatible with ROS2 Humble.

## Features

- Generates random test bag files with 2 different message types
- Creates both SQLite3 (.db3) and MCAP (.mcap) format bags
- Includes 80+ different ROS2 message types from common packages:
  - std_msgs
  - geometry_msgs
  - sensor_msgs
  - nav_msgs
  - tf2_msgs
- Automatically fixes metadata for ROS2 Humble compatibility
- Tests generated bags with `ros2 bag info` command

## Usage

```bash
python generate_test_bags.py
```

## Output

The script will create:
- `test_bag_sqlite3/` - SQLite3 format bag directory
- `test_bag_mcap/` - MCAP format bag directory

Each bag contains:
- 2 randomly selected message types
- 2 messages per topic (4 total messages)
- Proper timestamps with 100ms intervals
- ROS2 Humble compatible metadata

## Example Output

```
Generating test ROS2 bag files...

Selected message types:
  - sensor_msgs/msg/FluidPressure -> /test/sensor_msgs/fluid_pressure
  - geometry_msgs/msg/TransformStamped -> /test/geometry_msgs/transform_stamped

Creating SQLite3 bag file...
  Written message 1/2 for /test/sensor_msgs/fluid_pressure (sensor_msgs/msg/FluidPressure)
  Written message 1/2 for /test/geometry_msgs/transform_stamped (geometry_msgs/msg/TransformStamped)
  Written message 2/2 for /test/sensor_msgs/fluid_pressure (sensor_msgs/msg/FluidPressure)
  Written message 2/2 for /test/geometry_msgs/transform_stamped (geometry_msgs/msg/TransformStamped)
✓ Created test_bag_sqlite3.db3

Creating MCAP bag file...
  Written message 1/2 for /test/sensor_msgs/fluid_pressure (sensor_msgs/msg/FluidPressure)
  Written message 1/2 for /test/geometry_msgs/transform_stamped (geometry_msgs/msg/TransformStamped)
  Written message 2/2 for /test/sensor_msgs/fluid_pressure (sensor_msgs/msg/FluidPressure)
  Written message 2/2 for /test/geometry_msgs/transform_stamped (geometry_msgs/msg/TransformStamped)
✓ Created test_bag_mcap.mcap

✓ Successfully generated both bag files with 2 message types
  Total messages written: 4 (2 per topic)

Testing bag files with ros2 bag info...
  Testing test_bag_sqlite3...
    ✓ test_bag_sqlite3 is compatible with ros2 bag info
      Files:             test_bag_sqlite3.db3
      Bag size:          28.9 KiB
      Storage id:        sqlite3
  Testing test_bag_mcap...
    ✓ test_bag_mcap is compatible with ros2 bag info
      Files:             test_bag_mcap.mcap
      Bag size:          5.5 KiB
      Storage id:        mcap
```

## Requirements

- Python 3.8+
- rosbags library
- numpy
- ROS2 Humble (for testing compatibility)

## Supported Message Types

The script supports 80+ message types including:

### std_msgs
- Bool, Byte, Char, Empty, String
- Int8, Int16, Int32, Int64
- UInt8, UInt16, UInt32, UInt64
- Float32, Float64
- Header, ColorRGBA
- MultiArrayLayout, MultiArrayDimension
- All MultiArray types (Int32MultiArray, Float32MultiArray, etc.)

### geometry_msgs
- Point, Point32, Vector3, Quaternion
- Pose, PoseStamped, PoseWithCovariance
- Twist, TwistStamped, TwistWithCovariance
- Transform, TransformStamped
- Accel, AccelStamped, AccelWithCovariance
- Wrench, WrenchStamped
- Inertia, InertiaStamped

### sensor_msgs
- LaserScan, Image, PointCloud, PointCloud2
- JointState, Imu, MagneticField
- NavSatFix, Range, Temperature
- FluidPressure, Illuminance, BatteryState

### nav_msgs
- Odometry, Path, OccupancyGrid

### tf2_msgs
- TF2Error, TFMessage

## Notes

- The script automatically fixes metadata to be compatible with ROS2 Humble (version 5)
- All multi-array data uses proper numpy arrays with correct dtypes
- Generated bags are tested for compatibility with `ros2 bag info`
- Each run generates different random message types for variety
