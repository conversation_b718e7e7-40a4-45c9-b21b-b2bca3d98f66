#!/usr/bin/env python3
"""
Generate test ROS2 bag files for testing a Rust rosbag project.

This script creates two separate bag files (SQLite3 and MCAP format) with
randomly selected message types from the provided list, targeting ROS2 Foxy
distribution compatibility.

Requirements:
- rosbags package: pip install rosbags
- numpy: pip install numpy

Usage:
    python generate_test_bags.py

Output:
- test_bag_sqlite3.db3 (SQLite3 format)
- test_bag_mcap.mcap (MCAP format)
"""

import random
import time
from pathlib import Path
from typing import Any, Dict, List, Tuple

import numpy as np

try:
    from rosbags.rosbag2 import Writer
    from rosbags.rosbag2.enums import StoragePlugin
    from rosbags.typesys import Stores, get_typestore
    from rosbags.typesys.stores.ros2_foxy import (  # geometry_msgs; nav_msgs; sensor_msgs; std_msgs; stereo_msgs; tf2_msgs; builtin_interfaces for timestamps
        builtin_interfaces__msg__Duration,
        builtin_interfaces__msg__Time,
        geometry_msgs__msg__Accel,
        geometry_msgs__msg__AccelStamped,
        geometry_msgs__msg__AccelWithCovariance,
        geometry_msgs__msg__AccelWithCovarianceStamped,
        geometry_msgs__msg__Inertia,
        geometry_msgs__msg__InertiaStamped,
        geometry_msgs__msg__Point,
        geometry_msgs__msg__Point32,
        geometry_msgs__msg__PointStamped,
        geometry_msgs__msg__Polygon,
        geometry_msgs__msg__PolygonStamped,
        geometry_msgs__msg__Pose,
        geometry_msgs__msg__Pose2D,
        geometry_msgs__msg__PoseArray,
        geometry_msgs__msg__PoseStamped,
        geometry_msgs__msg__PoseWithCovariance,
        geometry_msgs__msg__PoseWithCovarianceStamped,
        geometry_msgs__msg__Quaternion,
        geometry_msgs__msg__QuaternionStamped,
        geometry_msgs__msg__Transform,
        geometry_msgs__msg__TransformStamped,
        geometry_msgs__msg__Twist,
        geometry_msgs__msg__TwistStamped,
        geometry_msgs__msg__TwistWithCovariance,
        geometry_msgs__msg__TwistWithCovarianceStamped,
        geometry_msgs__msg__Vector3,
        geometry_msgs__msg__Vector3Stamped,
        geometry_msgs__msg__Wrench,
        geometry_msgs__msg__WrenchStamped,
        nav_msgs__msg__GridCells,
        nav_msgs__msg__MapMetaData,
        nav_msgs__msg__OccupancyGrid,
        nav_msgs__msg__Odometry,
        nav_msgs__msg__Path,
        sensor_msgs__msg__BatteryState,
        sensor_msgs__msg__CameraInfo,
        sensor_msgs__msg__ChannelFloat32,
        sensor_msgs__msg__CompressedImage,
        sensor_msgs__msg__FluidPressure,
        sensor_msgs__msg__Illuminance,
        sensor_msgs__msg__Image,
        sensor_msgs__msg__Imu,
        sensor_msgs__msg__JointState,
        sensor_msgs__msg__Joy,
        sensor_msgs__msg__JoyFeedback,
        sensor_msgs__msg__JoyFeedbackArray,
        sensor_msgs__msg__LaserEcho,
        sensor_msgs__msg__LaserScan,
        sensor_msgs__msg__MagneticField,
        sensor_msgs__msg__MultiDOFJointState,
        sensor_msgs__msg__MultiEchoLaserScan,
        sensor_msgs__msg__NavSatFix,
        sensor_msgs__msg__NavSatStatus,
        sensor_msgs__msg__PointCloud,
        sensor_msgs__msg__PointCloud2,
        sensor_msgs__msg__PointField,
        sensor_msgs__msg__Range,
        sensor_msgs__msg__RegionOfInterest,
        sensor_msgs__msg__RelativeHumidity,
        sensor_msgs__msg__Temperature,
        sensor_msgs__msg__TimeReference,
        std_msgs__msg__Bool,
        std_msgs__msg__Byte,
        std_msgs__msg__ByteMultiArray,
        std_msgs__msg__Char,
        std_msgs__msg__ColorRGBA,
        std_msgs__msg__Empty,
        std_msgs__msg__Float32,
        std_msgs__msg__Float32MultiArray,
        std_msgs__msg__Float64,
        std_msgs__msg__Float64MultiArray,
        std_msgs__msg__Header,
        std_msgs__msg__Int8,
        std_msgs__msg__Int8MultiArray,
        std_msgs__msg__Int16,
        std_msgs__msg__Int16MultiArray,
        std_msgs__msg__Int32,
        std_msgs__msg__Int32MultiArray,
        std_msgs__msg__Int64,
        std_msgs__msg__Int64MultiArray,
        std_msgs__msg__MultiArrayDimension,
        std_msgs__msg__MultiArrayLayout,
        std_msgs__msg__String,
        std_msgs__msg__UInt8,
        std_msgs__msg__UInt8MultiArray,
        std_msgs__msg__UInt16,
        std_msgs__msg__UInt16MultiArray,
        std_msgs__msg__UInt32,
        std_msgs__msg__UInt32MultiArray,
        std_msgs__msg__UInt64,
        std_msgs__msg__UInt64MultiArray,
        stereo_msgs__msg__DisparityImage,
        tf2_msgs__msg__TF2Error,
        tf2_msgs__msg__TFMessage,
    )
except ImportError as e:
    print(f'Error importing rosbags: {e}')
    print('Please install rosbags: pip install rosbags')
    exit(1)


# Define all available message types with their classes and topic prefixes
MESSAGE_TYPES = {
    # geometry_msgs
    'geometry_msgs/msg/Accel': (geometry_msgs__msg__Accel, '/test/geometry_msgs/accel'),
    'geometry_msgs/msg/AccelStamped': (geometry_msgs__msg__AccelStamped, '/test/geometry_msgs/accel_stamped'),
    'geometry_msgs/msg/AccelWithCovariance': (
        geometry_msgs__msg__AccelWithCovariance,
        '/test/geometry_msgs/accel_with_covariance',
    ),
    'geometry_msgs/msg/AccelWithCovarianceStamped': (
        geometry_msgs__msg__AccelWithCovarianceStamped,
        '/test/geometry_msgs/accel_with_covariance_stamped',
    ),
    'geometry_msgs/msg/Inertia': (geometry_msgs__msg__Inertia, '/test/geometry_msgs/inertia'),
    'geometry_msgs/msg/InertiaStamped': (geometry_msgs__msg__InertiaStamped, '/test/geometry_msgs/inertia_stamped'),
    'geometry_msgs/msg/Point': (geometry_msgs__msg__Point, '/test/geometry_msgs/point'),
    'geometry_msgs/msg/Point32': (geometry_msgs__msg__Point32, '/test/geometry_msgs/point32'),
    'geometry_msgs/msg/PointStamped': (geometry_msgs__msg__PointStamped, '/test/geometry_msgs/point_stamped'),
    'geometry_msgs/msg/Polygon': (geometry_msgs__msg__Polygon, '/test/geometry_msgs/polygon'),
    'geometry_msgs/msg/PolygonStamped': (geometry_msgs__msg__PolygonStamped, '/test/geometry_msgs/polygon_stamped'),
    'geometry_msgs/msg/Pose': (geometry_msgs__msg__Pose, '/test/geometry_msgs/pose'),
    'geometry_msgs/msg/Pose2D': (geometry_msgs__msg__Pose2D, '/test/geometry_msgs/pose2d'),
    'geometry_msgs/msg/PoseArray': (geometry_msgs__msg__PoseArray, '/test/geometry_msgs/pose_array'),
    'geometry_msgs/msg/PoseStamped': (geometry_msgs__msg__PoseStamped, '/test/geometry_msgs/pose_stamped'),
    'geometry_msgs/msg/PoseWithCovariance': (
        geometry_msgs__msg__PoseWithCovariance,
        '/test/geometry_msgs/pose_with_covariance',
    ),
    'geometry_msgs/msg/PoseWithCovarianceStamped': (
        geometry_msgs__msg__PoseWithCovarianceStamped,
        '/test/geometry_msgs/pose_with_covariance_stamped',
    ),
    'geometry_msgs/msg/Quaternion': (geometry_msgs__msg__Quaternion, '/test/geometry_msgs/quaternion'),
    'geometry_msgs/msg/QuaternionStamped': (
        geometry_msgs__msg__QuaternionStamped,
        '/test/geometry_msgs/quaternion_stamped',
    ),
    'geometry_msgs/msg/Transform': (geometry_msgs__msg__Transform, '/test/geometry_msgs/transform'),
    'geometry_msgs/msg/TransformStamped': (
        geometry_msgs__msg__TransformStamped,
        '/test/geometry_msgs/transform_stamped',
    ),
    'geometry_msgs/msg/Twist': (geometry_msgs__msg__Twist, '/test/geometry_msgs/twist'),
    'geometry_msgs/msg/TwistStamped': (geometry_msgs__msg__TwistStamped, '/test/geometry_msgs/twist_stamped'),
    'geometry_msgs/msg/TwistWithCovariance': (
        geometry_msgs__msg__TwistWithCovariance,
        '/test/geometry_msgs/twist_with_covariance',
    ),
    'geometry_msgs/msg/TwistWithCovarianceStamped': (
        geometry_msgs__msg__TwistWithCovarianceStamped,
        '/test/geometry_msgs/twist_with_covariance_stamped',
    ),
    'geometry_msgs/msg/Vector3': (geometry_msgs__msg__Vector3, '/test/geometry_msgs/vector3'),
    'geometry_msgs/msg/Vector3Stamped': (geometry_msgs__msg__Vector3Stamped, '/test/geometry_msgs/vector3_stamped'),
    'geometry_msgs/msg/Wrench': (geometry_msgs__msg__Wrench, '/test/geometry_msgs/wrench'),
    'geometry_msgs/msg/WrenchStamped': (geometry_msgs__msg__WrenchStamped, '/test/geometry_msgs/wrench_stamped'),
    # nav_msgs
    'nav_msgs/msg/GridCells': (nav_msgs__msg__GridCells, '/test/nav_msgs/grid_cells'),
    'nav_msgs/msg/MapMetaData': (nav_msgs__msg__MapMetaData, '/test/nav_msgs/map_metadata'),
    'nav_msgs/msg/OccupancyGrid': (nav_msgs__msg__OccupancyGrid, '/test/nav_msgs/occupancy_grid'),
    'nav_msgs/msg/Odometry': (nav_msgs__msg__Odometry, '/test/nav_msgs/odometry'),
    'nav_msgs/msg/Path': (nav_msgs__msg__Path, '/test/nav_msgs/path'),
    # sensor_msgs
    'sensor_msgs/msg/BatteryState': (sensor_msgs__msg__BatteryState, '/test/sensor_msgs/battery_state'),
    'sensor_msgs/msg/CameraInfo': (sensor_msgs__msg__CameraInfo, '/test/sensor_msgs/camera_info'),
    'sensor_msgs/msg/ChannelFloat32': (sensor_msgs__msg__ChannelFloat32, '/test/sensor_msgs/channel_float32'),
    'sensor_msgs/msg/CompressedImage': (sensor_msgs__msg__CompressedImage, '/test/sensor_msgs/compressed_image'),
    'sensor_msgs/msg/FluidPressure': (sensor_msgs__msg__FluidPressure, '/test/sensor_msgs/fluid_pressure'),
    'sensor_msgs/msg/Illuminance': (sensor_msgs__msg__Illuminance, '/test/sensor_msgs/illuminance'),
    'sensor_msgs/msg/Image': (sensor_msgs__msg__Image, '/test/sensor_msgs/image'),
    'sensor_msgs/msg/Imu': (sensor_msgs__msg__Imu, '/test/sensor_msgs/imu'),
    'sensor_msgs/msg/JointState': (sensor_msgs__msg__JointState, '/test/sensor_msgs/joint_state'),
    'sensor_msgs/msg/Joy': (sensor_msgs__msg__Joy, '/test/sensor_msgs/joy'),
    'sensor_msgs/msg/JoyFeedback': (sensor_msgs__msg__JoyFeedback, '/test/sensor_msgs/joy_feedback'),
    'sensor_msgs/msg/JoyFeedbackArray': (sensor_msgs__msg__JoyFeedbackArray, '/test/sensor_msgs/joy_feedback_array'),
    'sensor_msgs/msg/LaserEcho': (sensor_msgs__msg__LaserEcho, '/test/sensor_msgs/laser_echo'),
    'sensor_msgs/msg/LaserScan': (sensor_msgs__msg__LaserScan, '/test/sensor_msgs/laser_scan'),
    'sensor_msgs/msg/MagneticField': (sensor_msgs__msg__MagneticField, '/test/sensor_msgs/magnetic_field'),
    'sensor_msgs/msg/MultiDOFJointState': (
        sensor_msgs__msg__MultiDOFJointState,
        '/test/sensor_msgs/multi_dof_joint_state',
    ),
    'sensor_msgs/msg/MultiEchoLaserScan': (
        sensor_msgs__msg__MultiEchoLaserScan,
        '/test/sensor_msgs/multi_echo_laser_scan',
    ),
    'sensor_msgs/msg/NavSatFix': (sensor_msgs__msg__NavSatFix, '/test/sensor_msgs/nav_sat_fix'),
    'sensor_msgs/msg/NavSatStatus': (sensor_msgs__msg__NavSatStatus, '/test/sensor_msgs/nav_sat_status'),
    'sensor_msgs/msg/PointCloud': (sensor_msgs__msg__PointCloud, '/test/sensor_msgs/point_cloud'),
    'sensor_msgs/msg/PointCloud2': (sensor_msgs__msg__PointCloud2, '/test/sensor_msgs/point_cloud2'),
    'sensor_msgs/msg/PointField': (sensor_msgs__msg__PointField, '/test/sensor_msgs/point_field'),
    'sensor_msgs/msg/Range': (sensor_msgs__msg__Range, '/test/sensor_msgs/range'),
    'sensor_msgs/msg/RegionOfInterest': (sensor_msgs__msg__RegionOfInterest, '/test/sensor_msgs/region_of_interest'),
    'sensor_msgs/msg/RelativeHumidity': (sensor_msgs__msg__RelativeHumidity, '/test/sensor_msgs/relative_humidity'),
    'sensor_msgs/msg/Temperature': (sensor_msgs__msg__Temperature, '/test/sensor_msgs/temperature'),
    'sensor_msgs/msg/TimeReference': (sensor_msgs__msg__TimeReference, '/test/sensor_msgs/time_reference'),
    # std_msgs
    'std_msgs/msg/Bool': (std_msgs__msg__Bool, '/test/std_msgs/bool'),
    'std_msgs/msg/Byte': (std_msgs__msg__Byte, '/test/std_msgs/byte'),
    'std_msgs/msg/ByteMultiArray': (std_msgs__msg__ByteMultiArray, '/test/std_msgs/byte_multi_array'),
    'std_msgs/msg/Char': (std_msgs__msg__Char, '/test/std_msgs/char'),
    'std_msgs/msg/ColorRGBA': (std_msgs__msg__ColorRGBA, '/test/std_msgs/color_rgba'),
    'std_msgs/msg/Empty': (std_msgs__msg__Empty, '/test/std_msgs/empty'),
    'std_msgs/msg/Float32': (std_msgs__msg__Float32, '/test/std_msgs/float32'),
    'std_msgs/msg/Float32MultiArray': (std_msgs__msg__Float32MultiArray, '/test/std_msgs/float32_multi_array'),
    'std_msgs/msg/Float64': (std_msgs__msg__Float64, '/test/std_msgs/float64'),
    'std_msgs/msg/Float64MultiArray': (std_msgs__msg__Float64MultiArray, '/test/std_msgs/float64_multi_array'),
    'std_msgs/msg/Header': (std_msgs__msg__Header, '/test/std_msgs/header'),
    'std_msgs/msg/Int16': (std_msgs__msg__Int16, '/test/std_msgs/int16'),
    'std_msgs/msg/Int16MultiArray': (std_msgs__msg__Int16MultiArray, '/test/std_msgs/int16_multi_array'),
    'std_msgs/msg/Int32': (std_msgs__msg__Int32, '/test/std_msgs/int32'),
    'std_msgs/msg/Int32MultiArray': (std_msgs__msg__Int32MultiArray, '/test/std_msgs/int32_multi_array'),
    'std_msgs/msg/Int64': (std_msgs__msg__Int64, '/test/std_msgs/int64'),
    'std_msgs/msg/Int64MultiArray': (std_msgs__msg__Int64MultiArray, '/test/std_msgs/int64_multi_array'),
    'std_msgs/msg/Int8': (std_msgs__msg__Int8, '/test/std_msgs/int8'),
    'std_msgs/msg/Int8MultiArray': (std_msgs__msg__Int8MultiArray, '/test/std_msgs/int8_multi_array'),
    'std_msgs/msg/MultiArrayDimension': (std_msgs__msg__MultiArrayDimension, '/test/std_msgs/multi_array_dimension'),
    'std_msgs/msg/MultiArrayLayout': (std_msgs__msg__MultiArrayLayout, '/test/std_msgs/multi_array_layout'),
    'std_msgs/msg/String': (std_msgs__msg__String, '/test/std_msgs/string'),
    'std_msgs/msg/UInt16': (std_msgs__msg__UInt16, '/test/std_msgs/uint16'),
    'std_msgs/msg/UInt16MultiArray': (std_msgs__msg__UInt16MultiArray, '/test/std_msgs/uint16_multi_array'),
    'std_msgs/msg/UInt32': (std_msgs__msg__UInt32, '/test/std_msgs/uint32'),
    'std_msgs/msg/UInt32MultiArray': (std_msgs__msg__UInt32MultiArray, '/test/std_msgs/uint32_multi_array'),
    'std_msgs/msg/UInt64': (std_msgs__msg__UInt64, '/test/std_msgs/uint64'),
    'std_msgs/msg/UInt64MultiArray': (std_msgs__msg__UInt64MultiArray, '/test/std_msgs/uint64_multi_array'),
    'std_msgs/msg/UInt8': (std_msgs__msg__UInt8, '/test/std_msgs/uint8'),
    'std_msgs/msg/UInt8MultiArray': (std_msgs__msg__UInt8MultiArray, '/test/std_msgs/uint8_multi_array'),
    # stereo_msgs
    'stereo_msgs/msg/DisparityImage': (stereo_msgs__msg__DisparityImage, '/test/stereo_msgs/disparity_image'),
    # tf2_msgs
    'tf2_msgs/msg/TF2Error': (tf2_msgs__msg__TF2Error, '/test/tf2_msgs/tf2_error'),
    'tf2_msgs/msg/TFMessage': (tf2_msgs__msg__TFMessage, '/test/tf2_msgs/tf_message'),
}


def create_header(frame_id: str = 'test_frame') -> std_msgs__msg__Header:
    """Create a standard ROS header with current timestamp."""
    current_time_ns = int(time.time() * 1e9)
    return std_msgs__msg__Header(
        stamp=builtin_interfaces__msg__Time(
            sec=current_time_ns // 1_000_000_000, nanosec=current_time_ns % 1_000_000_000
        ),
        frame_id=frame_id,
    )


def create_sample_message(msg_class: Any, msg_type: str) -> Any:
    """Create a sample message with realistic data for the given message type."""

    # Simple message types
    if msg_type == 'std_msgs/msg/Bool':
        return msg_class(data=True)
    elif msg_type == 'std_msgs/msg/Byte':
        return msg_class(data=42)
    elif msg_type == 'std_msgs/msg/Char':
        return msg_class(data=65)  # 'A'
    elif msg_type == 'std_msgs/msg/Empty':
        return msg_class()
    elif msg_type == 'std_msgs/msg/Float32':
        return msg_class(data=3.14159)
    elif msg_type == 'std_msgs/msg/Float64':
        return msg_class(data=2.71828)
    elif msg_type == 'std_msgs/msg/Int8':
        return msg_class(data=-42)
    elif msg_type == 'std_msgs/msg/Int16':
        return msg_class(data=-1000)
    elif msg_type == 'std_msgs/msg/Int32':
        return msg_class(data=-100000)
    elif msg_type == 'std_msgs/msg/Int64':
        return msg_class(data=-10000000000)
    elif msg_type == 'std_msgs/msg/UInt8':
        return msg_class(data=255)
    elif msg_type == 'std_msgs/msg/UInt16':
        return msg_class(data=65535)
    elif msg_type == 'std_msgs/msg/UInt32':
        return msg_class(data=4294967295)
    elif msg_type == 'std_msgs/msg/UInt64':
        return msg_class(data=18446744073709551615)
    elif msg_type == 'std_msgs/msg/String':
        return msg_class(data='Hello, ROS2!')
    elif msg_type == 'std_msgs/msg/Header':
        return create_header()
    elif msg_type == 'std_msgs/msg/ColorRGBA':
        return msg_class(r=1.0, g=0.5, b=0.0, a=0.8)

    # Geometry messages
    elif msg_type == 'geometry_msgs/msg/Point':
        return msg_class(x=1.0, y=2.0, z=3.0)
    elif msg_type == 'geometry_msgs/msg/Point32':
        return msg_class(x=1.5, y=2.5, z=3.5)
    elif msg_type == 'geometry_msgs/msg/Vector3':
        return msg_class(x=0.1, y=0.2, z=0.3)
    elif msg_type == 'geometry_msgs/msg/Quaternion':
        return msg_class(x=0.0, y=0.0, z=0.0, w=1.0)
    elif msg_type == 'geometry_msgs/msg/Pose':
        return msg_class(
            position=geometry_msgs__msg__Point(x=1.0, y=2.0, z=3.0),
            orientation=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0),
        )
    elif msg_type == 'geometry_msgs/msg/Pose2D':
        return msg_class(x=1.0, y=2.0, theta=0.5)
    elif msg_type == 'geometry_msgs/msg/Transform':
        return msg_class(
            translation=geometry_msgs__msg__Vector3(x=1.0, y=2.0, z=3.0),
            rotation=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0),
        )
    elif msg_type == 'geometry_msgs/msg/Twist':
        return msg_class(
            linear=geometry_msgs__msg__Vector3(x=1.0, y=0.0, z=0.0),
            angular=geometry_msgs__msg__Vector3(x=0.0, y=0.0, z=0.5),
        )
    elif msg_type == 'geometry_msgs/msg/Accel':
        return msg_class(
            linear=geometry_msgs__msg__Vector3(x=0.1, y=0.2, z=9.8),
            angular=geometry_msgs__msg__Vector3(x=0.0, y=0.0, z=0.1),
        )
    elif msg_type == 'geometry_msgs/msg/Wrench':
        return msg_class(
            force=geometry_msgs__msg__Vector3(x=10.0, y=5.0, z=2.0),
            torque=geometry_msgs__msg__Vector3(x=0.1, y=0.2, z=0.3),
        )
    elif msg_type == 'geometry_msgs/msg/Inertia':
        return msg_class(
            m=1.5,
            com=geometry_msgs__msg__Vector3(x=0.0, y=0.0, z=0.0),
            ixx=0.1,
            ixy=0.0,
            ixz=0.0,
            iyy=0.1,
            iyz=0.0,
            izz=0.1,
        )

    # Stamped geometry messages
    elif msg_type == 'geometry_msgs/msg/PointStamped':
        return msg_class(header=create_header(), point=geometry_msgs__msg__Point(x=1.0, y=2.0, z=3.0))
    elif msg_type == 'geometry_msgs/msg/PoseStamped':
        return msg_class(
            header=create_header(),
            pose=geometry_msgs__msg__Pose(
                position=geometry_msgs__msg__Point(x=1.0, y=2.0, z=3.0),
                orientation=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0),
            ),
        )
    elif msg_type == 'geometry_msgs/msg/TransformStamped':
        return msg_class(
            header=create_header(),
            child_frame_id='child_frame',
            transform=geometry_msgs__msg__Transform(
                translation=geometry_msgs__msg__Vector3(x=1.0, y=2.0, z=3.0),
                rotation=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0),
            ),
        )
    elif msg_type == 'geometry_msgs/msg/TwistStamped':
        return msg_class(
            header=create_header(),
            twist=geometry_msgs__msg__Twist(
                linear=geometry_msgs__msg__Vector3(x=1.0, y=0.0, z=0.0),
                angular=geometry_msgs__msg__Vector3(x=0.0, y=0.0, z=0.5),
            ),
        )
    elif msg_type == 'geometry_msgs/msg/AccelStamped':
        return msg_class(
            header=create_header(),
            accel=geometry_msgs__msg__Accel(
                linear=geometry_msgs__msg__Vector3(x=0.1, y=0.2, z=9.8),
                angular=geometry_msgs__msg__Vector3(x=0.0, y=0.0, z=0.1),
            ),
        )
    elif msg_type == 'geometry_msgs/msg/WrenchStamped':
        return msg_class(
            header=create_header(),
            wrench=geometry_msgs__msg__Wrench(
                force=geometry_msgs__msg__Vector3(x=10.0, y=5.0, z=2.0),
                torque=geometry_msgs__msg__Vector3(x=0.1, y=0.2, z=0.3),
            ),
        )
    elif msg_type == 'geometry_msgs/msg/Vector3Stamped':
        return msg_class(header=create_header(), vector=geometry_msgs__msg__Vector3(x=1.0, y=2.0, z=3.0))
    elif msg_type == 'geometry_msgs/msg/QuaternionStamped':
        return msg_class(header=create_header(), quaternion=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0))
    elif msg_type == 'geometry_msgs/msg/InertiaStamped':
        return msg_class(
            header=create_header(),
            inertia=geometry_msgs__msg__Inertia(
                m=1.5,
                com=geometry_msgs__msg__Vector3(x=0.0, y=0.0, z=0.0),
                ixx=0.1,
                ixy=0.0,
                ixz=0.0,
                iyy=0.1,
                iyz=0.0,
                izz=0.1,
            ),
        )
    elif msg_type == 'geometry_msgs/msg/Polygon':
        return msg_class(
            points=[
                geometry_msgs__msg__Point32(x=0.0, y=0.0, z=0.0),
                geometry_msgs__msg__Point32(x=1.0, y=0.0, z=0.0),
                geometry_msgs__msg__Point32(x=1.0, y=1.0, z=0.0),
                geometry_msgs__msg__Point32(x=0.0, y=1.0, z=0.0),
            ]
        )
    elif msg_type == 'geometry_msgs/msg/PolygonStamped':
        return msg_class(
            header=create_header(),
            polygon=geometry_msgs__msg__Polygon(
                points=[
                    geometry_msgs__msg__Point32(x=0.0, y=0.0, z=0.0),
                    geometry_msgs__msg__Point32(x=1.0, y=0.0, z=0.0),
                    geometry_msgs__msg__Point32(x=1.0, y=1.0, z=0.0),
                    geometry_msgs__msg__Point32(x=0.0, y=1.0, z=0.0),
                ]
            ),
        )
    elif msg_type == 'geometry_msgs/msg/PoseArray':
        return msg_class(
            header=create_header(),
            poses=[
                geometry_msgs__msg__Pose(
                    position=geometry_msgs__msg__Point(x=0.0, y=0.0, z=0.0),
                    orientation=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0),
                ),
                geometry_msgs__msg__Pose(
                    position=geometry_msgs__msg__Point(x=1.0, y=1.0, z=0.0),
                    orientation=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0),
                ),
            ],
        )
    elif msg_type == 'geometry_msgs/msg/PoseWithCovariance':
        return msg_class(
            pose=geometry_msgs__msg__Pose(
                position=geometry_msgs__msg__Point(x=1.0, y=2.0, z=3.0),
                orientation=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0),
            ),
            covariance=np.array([0.1] * 36, dtype=np.float64),
        )
    elif msg_type == 'geometry_msgs/msg/PoseWithCovarianceStamped':
        return msg_class(
            header=create_header(),
            pose=geometry_msgs__msg__PoseWithCovariance(
                pose=geometry_msgs__msg__Pose(
                    position=geometry_msgs__msg__Point(x=1.0, y=2.0, z=3.0),
                    orientation=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0),
                ),
                covariance=np.array([0.1] * 36, dtype=np.float64),
            ),
        )
    elif msg_type == 'geometry_msgs/msg/TwistWithCovariance':
        return msg_class(
            twist=geometry_msgs__msg__Twist(
                linear=geometry_msgs__msg__Vector3(x=1.0, y=0.0, z=0.0),
                angular=geometry_msgs__msg__Vector3(x=0.0, y=0.0, z=0.5),
            ),
            covariance=np.array([0.01] * 36, dtype=np.float64),
        )
    elif msg_type == 'geometry_msgs/msg/TwistWithCovarianceStamped':
        return msg_class(
            header=create_header(),
            twist=geometry_msgs__msg__TwistWithCovariance(
                twist=geometry_msgs__msg__Twist(
                    linear=geometry_msgs__msg__Vector3(x=1.0, y=0.0, z=0.0),
                    angular=geometry_msgs__msg__Vector3(x=0.0, y=0.0, z=0.5),
                ),
                covariance=np.array([0.01] * 36, dtype=np.float64),
            ),
        )
    elif msg_type == 'geometry_msgs/msg/AccelWithCovariance':
        return msg_class(
            accel=geometry_msgs__msg__Accel(
                linear=geometry_msgs__msg__Vector3(x=0.1, y=0.2, z=9.8),
                angular=geometry_msgs__msg__Vector3(x=0.0, y=0.0, z=0.1),
            ),
            covariance=np.array([0.1] * 36, dtype=np.float64),
        )
    elif msg_type == 'geometry_msgs/msg/AccelWithCovarianceStamped':
        return msg_class(
            header=create_header(),
            accel=geometry_msgs__msg__AccelWithCovariance(
                accel=geometry_msgs__msg__Accel(
                    linear=geometry_msgs__msg__Vector3(x=0.1, y=0.2, z=9.8),
                    angular=geometry_msgs__msg__Vector3(x=0.0, y=0.0, z=0.1),
                ),
                covariance=np.array([0.1] * 36, dtype=np.float64),
            ),
        )

    # Default fallback - try to create with minimal parameters
    else:
        try:
            return msg_class()
        except Exception:
            # For complex messages, we'll need specific handling
            return create_complex_message(msg_class, msg_type)


def create_complex_message(msg_class: Any, msg_type: str) -> Any:
    """Create complex messages that require specific field initialization."""

    # Sensor messages
    if msg_type == 'sensor_msgs/msg/LaserScan':
        return msg_class(
            header=create_header(),
            angle_min=-1.57,
            angle_max=1.57,
            angle_increment=0.01,
            time_increment=0.0,
            scan_time=0.1,
            range_min=0.1,
            range_max=10.0,
            ranges=np.array([1.0, 2.0, 3.0, 4.0, 5.0], dtype=np.float32),
            intensities=np.array([100.0, 200.0, 300.0, 400.0, 500.0], dtype=np.float32),
        )
    elif msg_type == 'sensor_msgs/msg/Image':
        return msg_class(
            header=create_header(),
            height=480,
            width=640,
            encoding='rgb8',
            is_bigendian=0,
            step=1920,  # width * 3 for rgb8
            data=bytes([255, 0, 0] * 10),  # Small red pixel data
        )
    elif msg_type == 'sensor_msgs/msg/PointCloud2':
        return msg_class(
            header=create_header(),
            height=1,
            width=3,
            fields=[
                sensor_msgs__msg__PointField(name='x', offset=0, datatype=7, count=1),
                sensor_msgs__msg__PointField(name='y', offset=4, datatype=7, count=1),
                sensor_msgs__msg__PointField(name='z', offset=8, datatype=7, count=1),
            ],
            is_bigendian=False,
            point_step=12,
            row_step=36,
            data=bytes([0] * 36),
            is_dense=True,
        )
    elif msg_type == 'sensor_msgs/msg/JointState':
        return msg_class(
            header=create_header(),
            name=['joint1', 'joint2', 'joint3'],
            position=np.array([0.1, 0.2, 0.3], dtype=np.float64),
            velocity=np.array([0.01, 0.02, 0.03], dtype=np.float64),
            effort=np.array([1.0, 2.0, 3.0], dtype=np.float64),
        )
    elif msg_type == 'sensor_msgs/msg/Imu':
        return msg_class(
            header=create_header(),
            orientation=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0),
            orientation_covariance=np.array([0.1] * 9, dtype=np.float64),
            angular_velocity=geometry_msgs__msg__Vector3(x=0.01, y=0.02, z=0.03),
            angular_velocity_covariance=np.array([0.01] * 9, dtype=np.float64),
            linear_acceleration=geometry_msgs__msg__Vector3(x=0.1, y=0.2, z=9.8),
            linear_acceleration_covariance=np.array([0.1] * 9, dtype=np.float64),
        )
    elif msg_type == 'sensor_msgs/msg/Range':
        return msg_class(
            header=create_header(),
            radiation_type=0,  # ULTRASOUND
            field_of_view=0.1,
            min_range=0.02,
            max_range=2.0,
            range=1.5,
        )
    elif msg_type == 'sensor_msgs/msg/Temperature':
        return msg_class(header=create_header(), temperature=25.5, variance=0.1)
    elif msg_type == 'sensor_msgs/msg/FluidPressure':
        return msg_class(header=create_header(), fluid_pressure=101325.0, variance=100.0)
    elif msg_type == 'sensor_msgs/msg/MagneticField':
        return msg_class(
            header=create_header(),
            magnetic_field=geometry_msgs__msg__Vector3(x=0.1, y=0.2, z=0.3),
            magnetic_field_covariance=np.array([0.01] * 9, dtype=np.float64),
        )
    elif msg_type == 'sensor_msgs/msg/NavSatFix':
        return msg_class(
            header=create_header(),
            status=sensor_msgs__msg__NavSatStatus(status=0, service=1),
            latitude=37.7749,
            longitude=-122.4194,
            altitude=100.0,
            position_covariance=np.array([1.0] * 9, dtype=np.float64),
            position_covariance_type=1,
        )
    elif msg_type == 'sensor_msgs/msg/BatteryState':
        return msg_class(
            header=create_header(),
            voltage=12.6,
            temperature=25.0,
            current=-5.0,
            charge=50.0,
            capacity=100.0,
            design_capacity=100.0,
            percentage=0.5,
            power_supply_status=2,  # DISCHARGING
            power_supply_health=1,  # GOOD
            power_supply_technology=1,  # LION
            present=True,
            cell_voltage=[3.7, 3.7, 3.7],
            cell_temperature=[25.0, 25.0, 25.0],
            location='battery_compartment',
            serial_number='BAT123456',
        )

    # Nav messages
    elif msg_type == 'nav_msgs/msg/Odometry':
        return msg_class(
            header=create_header(),
            child_frame_id='base_link',
            pose=geometry_msgs__msg__PoseWithCovariance(
                pose=geometry_msgs__msg__Pose(
                    position=geometry_msgs__msg__Point(x=1.0, y=2.0, z=0.0),
                    orientation=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0),
                ),
                covariance=np.array([0.1] * 36, dtype=np.float64),
            ),
            twist=geometry_msgs__msg__TwistWithCovariance(
                twist=geometry_msgs__msg__Twist(
                    linear=geometry_msgs__msg__Vector3(x=1.0, y=0.0, z=0.0),
                    angular=geometry_msgs__msg__Vector3(x=0.0, y=0.0, z=0.1),
                ),
                covariance=np.array([0.01] * 36, dtype=np.float64),
            ),
        )
    elif msg_type == 'nav_msgs/msg/Path':
        return msg_class(
            header=create_header(),
            poses=[
                geometry_msgs__msg__PoseStamped(
                    header=create_header(),
                    pose=geometry_msgs__msg__Pose(
                        position=geometry_msgs__msg__Point(x=0.0, y=0.0, z=0.0),
                        orientation=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0),
                    ),
                ),
                geometry_msgs__msg__PoseStamped(
                    header=create_header(),
                    pose=geometry_msgs__msg__Pose(
                        position=geometry_msgs__msg__Point(x=1.0, y=1.0, z=0.0),
                        orientation=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0),
                    ),
                ),
            ],
        )
    elif msg_type == 'nav_msgs/msg/OccupancyGrid':
        return msg_class(
            header=create_header(),
            info=nav_msgs__msg__MapMetaData(
                map_load_time=builtin_interfaces__msg__Time(sec=0, nanosec=0),
                resolution=0.05,
                width=10,
                height=10,
                origin=geometry_msgs__msg__Pose(
                    position=geometry_msgs__msg__Point(x=0.0, y=0.0, z=0.0),
                    orientation=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0),
                ),
            ),
            data=[-1] * 100,  # 10x10 grid with unknown values
        )

    # Multi-array messages
    elif msg_type == 'std_msgs/msg/Float32MultiArray':
        return msg_class(
            layout=std_msgs__msg__MultiArrayLayout(
                dim=[std_msgs__msg__MultiArrayDimension(label='x', size=3, stride=3)], data_offset=0
            ),
            data=[1.1, 2.2, 3.3],
        )
    elif msg_type == 'std_msgs/msg/Int32MultiArray':
        return msg_class(
            layout=std_msgs__msg__MultiArrayLayout(
                dim=[std_msgs__msg__MultiArrayDimension(label='x', size=3, stride=3)], data_offset=0
            ),
            data=[1, 2, 3],
        )

    # Default fallback for any remaining complex types
    else:
        # Try to create with empty constructor
        return msg_class()


def select_random_message_types(num_types: int = 2) -> List[Tuple[str, Any, str]]:
    """Select random message types from the available list."""
    selected_types = random.sample(list(MESSAGE_TYPES.keys()), num_types)
    return [(msg_type, MESSAGE_TYPES[msg_type][0], MESSAGE_TYPES[msg_type][1]) for msg_type in selected_types]


def create_bag_file(bag_path: str, storage_format: str, selected_types: List[Tuple[str, Any, str]]) -> None:
    """Create a bag file with the specified format and message types."""
    typestore = get_typestore(Stores.ROS2_FOXY)

    # Determine storage plugin based on format
    if storage_format.lower() == 'sqlite3':
        storage_plugin = StoragePlugin.SQLITE3
    elif storage_format.lower() == 'mcap':
        storage_plugin = StoragePlugin.MCAP
    else:
        error_msg = f'Unsupported storage format: {storage_format}'
        raise ValueError(error_msg)

    with Writer(bag_path, version=Writer.VERSION_LATEST, storage_plugin=storage_plugin) as writer:
        connections = []

        # Add connections for each selected message type
        for msg_type, msg_class, topic_name in selected_types:
            connection = writer.add_connection(topic_name, msg_type, typestore=typestore)
            connections.append((connection, msg_class, msg_type, topic_name))

        # Write 2 messages per topic
        base_timestamp = int(time.time() * 1e9)
        message_count = 0

        for i in range(2):  # 2 messages per topic
            for connection, msg_class, msg_type, topic_name in connections:
                # Create sample message
                message = create_sample_message(msg_class, msg_type)

                # Serialize and write
                timestamp = base_timestamp + (message_count * 100_000_000)  # 100ms intervals
                serialized_data = typestore.serialize_cdr(message, msg_type)
                writer.write(connection, timestamp, serialized_data)
                message_count += 1

                print(f'  Written message {i + 1}/2 for {topic_name} ({msg_type})')


def main():
    """Main function to generate test bag files."""
    print('Generating test ROS2 bag files...')

    # Select 2 random message types
    selected_types = select_random_message_types(2)

    print(f'\nSelected message types:')
    for msg_type, _, topic_name in selected_types:
        print(f'  - {msg_type} -> {topic_name}')

    # Create SQLite3 bag
    print(f'\nCreating SQLite3 bag file...')
    create_bag_file('test_bag_sqlite3', 'sqlite3', selected_types)
    print(f'✓ Created test_bag_sqlite3.db3')

    # Create MCAP bag
    print(f'\nCreating MCAP bag file...')
    create_bag_file('test_bag_mcap', 'mcap', selected_types)
    print(f'✓ Created test_bag_mcap.mcap')

    print(f'\n✓ Successfully generated both bag files with {len(selected_types)} message types')
    print(f'  Total messages written: {len(selected_types) * 2} (2 per topic)')


if __name__ == '__main__':
    main()
