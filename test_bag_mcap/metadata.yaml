rosbag2_bagfile_information:
  compression_format: ''
  compression_mode: ''
  custom_data: null
  duration:
    nanoseconds: 300000000
  files:
  - duration:
      nanoseconds: 300000000
    message_count: 4
    path: test_bag_mcap.mcap
    starting_time:
      nanoseconds_since_epoch: 1749313436802581760
  message_count: 4
  relative_file_paths:
  - test_bag_mcap.mcap
  ros_distro: rosbags
  starting_time:
    nanoseconds_since_epoch: 1749313436802581760
  storage_identifier: mcap
  topics_with_message_count:
  - message_count: 2
    topic_metadata:
      name: /test/geometry_msgs/wrench_stamped
      offered_qos_profiles: []
      serialization_format: cdr
      type: geometry_msgs/msg/WrenchStamped
      type_description_hash: RIHS01_8dc3deaf06b2ab281f9f9a742a8961c328ca7cec16e3fd6586d3a5c83fa78f77
  - message_count: 2
    topic_metadata:
      name: /test/std_msgs/bool
      offered_qos_profiles: []
      serialization_format: cdr
      type: std_msgs/msg/Bool
      type_description_hash: RIHS01_feb91e995ff9ebd09c0cb3d2aed18b11077585839fb5db80193b62d74528f6c9
  version: 9
